'use client';
import React from "react";
import DiscoverContextPanel from "./discover-context-panel";
import DiscoverMainContent from "./discover-main-content";

interface DiscoverPageProps {
  activeSection: string;
}

export default function DiscoverPage({ activeSection }: DiscoverPageProps) {
  return (
    <div className="h-full bg-background text-foreground flex overflow-hidden">
      {/* Left contextual panel */}
      <DiscoverContextPanel active={activeSection} />
      {/* Main dashboard content */}
      <DiscoverMainContent active={activeSection} />
    </div>
  );
}