import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useOnboarding } from '@/features/onboarding/onboarding-context';

export default function Step1() {
  const { step, setStep, data, setData } = useOnboarding();
  const realName = data.realName || '';
  const artistName = data.artistName || '';

  const canContinue = realName.trim() && artistName.trim();

  return (
    <div className="flex flex-col w-full max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4 mt-2">Tell us about you</h1>
      <div className="mb-4">
        <label className="block text-sm font-medium mb-1 text-foreground">Write your real name</label>
        <Input
          type="text"
          placeholder="e.g. <PERSON>"
          value={realName}
          onChange={e => setData({ realName: e.target.value })}
          className="w-full bg-card text-foreground border border-input placeholder-muted-foreground"
        />
      </div>
      <div className="mb-1">
        <label className="block text-sm font-medium mb-1 text-foreground">Write your artist name</label>
        <Input
          type="text"
          placeholder="e.g. Savvy Anna"
          value={artistName}
          onChange={e => setData({ artistName: e.target.value })}
          className="w-full bg-card text-foreground border border-input placeholder-muted-foreground"
        />
      </div>
      <div className="text-xs text-muted-foreground mb-8">This is the name your fans will see</div>
      <Button
        className="w-full h-12 text-base font-semibold"
        disabled={!canContinue}
        onClick={() => setStep(step + 1)}
      >
        Continue
      </Button>
    </div>
  );
}
