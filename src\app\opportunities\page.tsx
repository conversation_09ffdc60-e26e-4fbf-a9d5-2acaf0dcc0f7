"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, MapPin, Calendar, DollarSign, Plus, MessageCircle, Eye, Music, Users } from "lucide-react"
import Link from "next/link"
import type { Opportunity } from "../../types/opportunity"

// Mock data
const mockOpportunities: Opportunity[] = [
  {
    id: "1",
    title: "Looking for Jazz Drummer",
    role: "Drum<PERSON>",
    description:
      "We need an experienced jazz drummer for our upcoming album recording. Must be comfortable with complex time signatures and improvisation.",
    type: "session",
    postedBy: {
      id: "1",
      name: "<PERSON><PERSON>",
      type: "artist",
      avatar: "/placeholder.svg?height=40&width=40",
      verified: true,
      rating: 4.9,
      totalReviews: 18,
    },
    genre: ["Jazz", "Fusion"],
    timeline: {
      startDate: "2024-06-15",
      endDate: "2024-07-30",
      duration: "1.5 months",
    },
    location: {
      type: "remote",
    },
    payment: {
      type: "paid",
      amount: 8000,
      currency: "₹",
    },
    associatedProject: {
      id: "album-1",
      name: "Midnight Sessions",
      type: "album",
    },
    requirements: ["5+ years experience", "Own drum kit", "Recording setup"],
    remote: true,
    postedAt: "2024-01-15",
    deadline: "2024-06-10",
    status: "open",
    applicationsCount: 12,
    tags: ["jazz", "drummer", "session", "recording"],
    createdAt: "2024-01-15",
    updatedAt: "2024-01-15",
  },
  {
    id: "2",
    title: "Session Guitarist Needed",
    role: "Guitarist",
    description: "Looking for a clean guitarist to record 2 tracks remotely. Influences: John Mayer, Arctic Monkeys.",
    type: "session",
    postedBy: {
      id: "2",
      name: "The Midnight Band",
      type: "band",
      avatar: "/placeholder.svg?height=40&width=40",
      verified: true,
      rating: 4.8,
      totalReviews: 24,
    },
    genre: ["Indie", "Alternative"],
    timeline: {
      startDate: "2024-07-01",
      endDate: "2024-07-31",
      duration: "1 month",
    },
    location: {
      type: "remote",
    },
    payment: {
      type: "paid",
      amount: 5000,
      currency: "₹",
    },
    associatedProject: {
      id: "album-2",
      name: "Night Drive EP",
      type: "album",
    },
    requirements: ["Professional recording setup", "Experience with indie rock"],
    remote: true,
    postedAt: "2024-01-10",
    deadline: "2024-06-15",
    status: "open",
    applicationsCount: 8,
    tags: ["guitar", "session", "indie", "recording"],
    createdAt: "2024-01-10",
    updatedAt: "2024-01-10",
  },
  {
    id: "3",
    title: "Vocalist for Electronic Project",
    role: "Vocalist",
    description: "Seeking a versatile vocalist for an experimental electronic music project. Open to all vocal styles.",
    type: "collaboration",
    postedBy: {
      id: "3",
      name: "Alex Producer",
      type: "person",
      avatar: "/placeholder.svg?height=40&width=40",
      verified: false,
      rating: 4.5,
      totalReviews: 12,
    },
    genre: ["Electronic", "Experimental"],
    timeline: {
      startDate: "2024-08-01",
      endDate: "2024-09-15",
      duration: "1.5 months",
    },
    location: {
      type: "hybrid",
      city: "Mumbai",
      country: "India",
    },
    payment: {
      type: "negotiable",
    },
    requirements: ["Versatile vocal range", "Experience with electronic music"],
    remote: false,
    postedAt: "2024-01-12",
    deadline: "2024-07-20",
    status: "open",
    applicationsCount: 15,
    tags: ["vocals", "electronic", "experimental", "collaboration"],
    createdAt: "2024-01-12",
    updatedAt: "2024-01-12",
  },
]

export default function OpportunitiesPage() {
  const [searchTerm, setSearchTerm] = useState("")
  const [selectedGenre, setSelectedGenre] = useState<string>("")
  const [selectedLocation, setSelectedLocation] = useState<string>("")
  const [selectedPayment, setSelectedPayment] = useState<string>("")

  const filteredOpportunities = mockOpportunities.filter((opportunity) => {
    const matchesSearch =
      opportunity.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (opportunity.role && opportunity.role.toLowerCase().includes(searchTerm.toLowerCase()))
    const matchesGenre = !selectedGenre || opportunity.genre.includes(selectedGenre)
    const matchesLocation = !selectedLocation ||
      (typeof opportunity.location === 'string'
        ? opportunity.location === selectedLocation
        : opportunity.location.type === selectedLocation)
    const matchesPayment = !selectedPayment ||
      (typeof opportunity.payment === 'string'
        ? opportunity.payment === selectedPayment
        : opportunity.payment.type === selectedPayment)

    return matchesSearch && matchesGenre && matchesLocation && matchesPayment
  })

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold">Music Opportunities</h1>
            <p className="text-muted-foreground">Find your next gig or collaboration</p>
          </div>
          <Link href="/opportunities/new">
            <Button className="gap-2">
              <Plus className="w-4 h-4" />
              Post Opportunity
            </Button>
          </Link>
        </div>

        {/* Search and Filters */}
        <Card className="mb-8">
          <CardContent className="pt-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
                  <Input
                    placeholder="Search by role, genre, or keywords..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex flex-wrap gap-2">
                <Select value={selectedGenre} onValueChange={setSelectedGenre}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Genre" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Genres</SelectItem>
                    <SelectItem value="Jazz">Jazz</SelectItem>
                    <SelectItem value="Rock">Rock</SelectItem>
                    <SelectItem value="Pop">Pop</SelectItem>
                    <SelectItem value="Electronic">Electronic</SelectItem>
                    <SelectItem value="Indie">Indie</SelectItem>
                    <SelectItem value="Alternative">Alternative</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedLocation} onValueChange={setSelectedLocation}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Location" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Locations</SelectItem>
                    <SelectItem value="remote">Remote</SelectItem>
                    <SelectItem value="in-person">In-Person</SelectItem>
                    <SelectItem value="hybrid">Hybrid</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={selectedPayment} onValueChange={setSelectedPayment}>
                  <SelectTrigger className="w-[140px]">
                    <SelectValue placeholder="Payment" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="paid">Paid</SelectItem>
                    <SelectItem value="unpaid">Unpaid</SelectItem>
                    <SelectItem value="negotiable">Negotiable</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Results Count */}
        <div className="mb-6">
          <p className="text-muted-foreground">{filteredOpportunities.length} opportunities found</p>
        </div>

        {/* Opportunities Grid */}
        <div className="grid gap-6">
          {filteredOpportunities.map((opportunity) => (
            <Card key={opportunity.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="flex items-start gap-4 mb-4">
                      <Avatar className="w-12 h-12">
                        <AvatarImage src={opportunity.postedBy.avatar || "/placeholder.svg"} />
                        <AvatarFallback>
                          {opportunity.postedBy.name
                            .split(" ")
                            .map((n: string) => n[0])
                            .join("")}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <h3 className="text-xl font-semibold mb-1">{opportunity.title}</h3>
                        <p className="text-muted-foreground text-sm mb-2">Posted by {opportunity.postedBy.name}</p>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {opportunity.genre.map((genre: string) => (
                            <Badge key={genre} variant="secondary">
                              {genre}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>

                    <p className="text-muted-foreground mb-4 line-clamp-2">{opportunity.description}</p>

                    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground">
                      <div className="flex items-center gap-1">
                        <MapPin className="w-4 h-4" />
                        <span className="capitalize">
                          {typeof opportunity.location === 'string'
                            ? opportunity.location
                            : opportunity.location.type}
                        </span>
                        {typeof opportunity.location === 'object' && opportunity.location.city && (
                          <span> • {opportunity.location.city}</span>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        <DollarSign className="w-4 h-4" />
                        <span className="capitalize">
                          {typeof opportunity.payment === 'string'
                            ? opportunity.payment
                            : opportunity.payment.type === "paid"
                            ? `${opportunity.payment.currency}${opportunity.payment.amount?.toLocaleString()}`
                            : opportunity.payment.type}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>
                          {new Date(opportunity.timeline.startDate).toLocaleDateString()} -
                          {new Date(opportunity.timeline.endDate).toLocaleDateString()}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span>{opportunity.applicationsCount} applicants</span>
                      </div>
                    </div>

                    {opportunity.associatedProject && (
                      <div className="mt-3 p-3 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-2 text-sm">
                          <Music className="w-4 h-4" />
                          <span>Project: </span>
                          <span className="font-medium text-primary cursor-pointer hover:underline">
                            {opportunity.associatedProject.name}
                          </span>
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="flex flex-col gap-2 lg:w-auto w-full">
                    <Link href={`/opportunities/${opportunity.id}`}>
                      <Button variant="outline" className="w-full gap-2">
                        <Eye className="w-4 h-4" />
                        View Details
                      </Button>
                    </Link>
                    <Button className="w-full gap-2">
                      <MessageCircle className="w-4 h-4" />
                      Apply
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredOpportunities.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Music className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No opportunities found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your search criteria or check back later for new opportunities.
              </p>
              <Link href="/opportunities/new">
                <Button>Post an Opportunity</Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
