// Opportunity and application types

export interface Opportunity {
  id: string
  title: string
  role?: string
  description: string
  type: 'collaboration' | 'gig' | 'session' | 'production'
  genre: string[]
  payment: string | { type: string; amount?: number; currency?: string }
  location: string | { type: string; city?: string; country?: string }
  remote: boolean
  timeline: {
    startDate: string
    endDate: string
    duration: string
  }
  requirements: string[]
  postedBy: {
    id: string
    name: string
    type?: string
    avatar: string
    verified: boolean
    rating: number
    totalReviews: number
  }
  postedAt: string
  deadline: string
  status: 'open' | 'closed' | 'in-progress' | 'completed'
  applicationsCount: number
  tags: string[]
  associatedProject?: {
    id: string
    name: string
    type: string
  }
  createdAt?: string
  updatedAt?: string
}

export interface Application {
  id: string
  opportunityId: string
  opportunity: {
    id: string
    title: string
    role?: string
    description: string
    postedBy: {
      id: string
      name: string
      type: string
      avatar: string
    }
    genre: string[]
    timeline: {
      startDate: string
      endDate: string
    }
    location: {
      type: string
    }
    payment: {
      type: string
      amount?: number
      currency?: string
    }
    requirements: string[]
    status: string
    applicationsCount: number
    createdAt: string
    updatedAt: string
    associatedProject?: {
      name: string
    }
  }
  applicantId: string
  applicant: {
    id: string
    name: string
  }
  status: 'pending' | 'accepted' | 'rejected' | 'withdrawn'
  appliedAt: string
  message: string
  response?: {
    message: string
    respondedAt: string
  }
}

export interface OpportunityFilter {
  type?: string[]
  genre?: string[]
  location?: string
  remote?: boolean
  payment?: {
    min?: number
    max?: number
    type?: 'hourly' | 'fixed' | 'revenue-share'
  }
  timeline?: {
    startDate?: string
    endDate?: string
  }
  search?: string
}

export interface OpportunitySort {
  field: 'postedAt' | 'deadline' | 'payment' | 'title'
  direction: 'asc' | 'desc'
}

export type OpportunityType = 'collaboration' | 'gig' | 'session' | 'production'
export type ApplicationStatus = 'pending' | 'accepted' | 'rejected' | 'withdrawn'
export type OpportunityStatus = 'open' | 'closed' | 'in-progress' | 'completed'
