'use client'
import SongPage from '@/features/song/component/song-page';
import { useSearchParams } from 'next/navigation';
import { Suspense } from 'react';

function SongContent() {
  const searchParams = useSearchParams()
  const songId = searchParams.get('id')

  if (!songId) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Album Not Found</h1>
          <p className="text-muted-foreground">Please provide a valid album ID in the URL.</p>
          <p className="text-sm text-muted-foreground mt-2">
            Example: /album?id=your-album-id
          </p>
        </div>
      </div>
    )
  }

  return <SongPage songId={songId} />
}

export default function Song() {
 return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading Song Details...</p>
        </div>
      </div>
    }>
      <SongContent />
    </Suspense>
  )
}
