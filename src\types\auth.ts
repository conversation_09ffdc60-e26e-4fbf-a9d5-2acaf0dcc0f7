// Centralized authentication types

export interface AuthUser {
  userId: string
  username: string
  email?: string
}

export interface AuthResponse {
  success: boolean
  error?: string
}

export interface LoginResponse extends AuthResponse {
  user?: AuthUser
  requiresVerification?: boolean
  email?: string
}

export interface SignupResponse extends AuthResponse {
  userId?: string
  nextStep?: {
    signUpStep: string
    codeDeliveryDetails?: {
      destination?: string
      deliveryMedium?: string
    }
  }
}

export interface VerificationResponse extends AuthResponse {
  user?: AuthUser
}

// Auth form data types
export interface LoginFormData {
  email: string
  password: string
}

export interface SignupFormData {
  email: string
  password: string
  confirmPassword: string
  username?: string
}

export interface ForgotPasswordFormData {
  email: string
}

export interface ResetPasswordFormData {
  email: string
  code: string
  newPassword: string
  confirmPassword: string
}

export interface EmailVerificationFormData {
  code: string
}

// Auth state types
export type AuthStatus = 'loading' | 'authenticated' | 'unauthenticated'

export interface AuthState {
  user: AuthUser | null
  status: AuthStatus
  isLoading: boolean
  isAuthenticated: boolean
}

// Auth error types
export type AuthErrorCode = 
  | 'UserNotConfirmedException'
  | 'NotAuthorizedException'
  | 'UserNotFoundException'
  | 'InvalidParameterException'
  | 'TooManyRequestsException'
  | 'UsernameExistsException'
  | 'InvalidPasswordException'
  | 'CodeMismatchException'
  | 'ExpiredCodeException'
  | 'UnknownError'

export interface AuthError {
  code: AuthErrorCode
  message: string
  originalError?: Error
}

// Auth flow types
export type AuthFlow = 'signin' | 'signup' | 'forgot-password' | 'verify-email'
export type SignupStep = 'signup' | 'verification'
export type ForgotPasswordStep = 'request' | 'reset' | 'success'
