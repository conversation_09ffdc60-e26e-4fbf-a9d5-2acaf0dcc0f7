"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Eye, Users, Plus, Edit, Trash2, Calendar, DollarSign } from "lucide-react"
import Link from "next/link"
import type { Opportunity } from "../../../types/opportunity"

// <PERSON><PERSON> posted opportunities data
const mockPostedOpportunities: Opportunity[] = [
  {
    id: "posted-1",
    title: "Looking for <PERSON> Drummer",
    role: "Drum<PERSON>",
    description: "We need an experienced jazz drummer for our upcoming album recording.",
    type: "session",
    postedBy: {
      id: "current-user",
      name: "You",
      type: "artist",
      avatar: "/placeholder.svg?height=40&width=40",
      verified: true,
      rating: 4.9,
      totalReviews: 18,
    },
    genre: ["Jazz", "Fusion"],
    timeline: {
      startDate: "2024-06-15",
      endDate: "2024-07-30",
      duration: "1.5 months",
    },
    location: {
      type: "remote",
    },
    payment: {
      type: "paid",
      amount: 8000,
      currency: "₹",
    },
    requirements: ["5+ years experience", "Own drum kit", "Recording setup"],
    remote: true,
    postedAt: "2024-01-15",
    deadline: "2024-06-10",
    status: "open",
    applicationsCount: 12,
    tags: ["jazz", "drummer", "session", "recording"],
    createdAt: "2024-01-15",
    updatedAt: "2024-01-15",
  },
  {
    id: "posted-2",
    title: "Vocalist for Pop Album",
    role: "Vocalist",
    description: "Seeking a versatile vocalist for a pop album project.",
    type: "gig",
    postedBy: {
      id: "current-user",
      name: "You",
      type: "artist",
      avatar: "/placeholder.svg?height=40&width=40",
      verified: true,
      rating: 4.9,
      totalReviews: 18,
    },
    genre: ["Pop", "R&B"],
    timeline: {
      startDate: "2024-08-01",
      endDate: "2024-09-30",
      duration: "2 months",
    },
    location: {
      type: "in-person",
      city: "Mumbai",
      country: "India",
    },
    payment: {
      type: "paid",
      amount: 15000,
      currency: "₹",
    },
    requirements: ["Professional vocal training", "Studio experience"],
    remote: false,
    postedAt: "2024-01-10",
    deadline: "2024-07-20",
    status: "closed",
    applicationsCount: 25,
    tags: ["vocals", "pop", "album", "recording"],
    createdAt: "2024-01-10",
    updatedAt: "2024-01-20",
  },
  {
    id: "posted-3",
    title: "Session Bassist Needed",
    role: "Bassist",
    description: "Looking for a session bassist for indie rock tracks.",
    type: "session",
    postedBy: {
      id: "current-user",
      name: "You",
      type: "artist",
      avatar: "/placeholder.svg?height=40&width=40",
      verified: true,
      rating: 4.9,
      totalReviews: 18,
    },
    genre: ["Indie", "Rock"],
    timeline: {
      startDate: "2024-07-15",
      endDate: "2024-08-15",
      duration: "1 month",
    },
    location: {
      type: "remote",
    },
    payment: {
      type: "negotiable",
    },
    requirements: ["Own bass guitar", "Recording setup"],
    remote: true,
    postedAt: "2024-01-18",
    deadline: "2024-07-10",
    status: "open",
    applicationsCount: 6,
    tags: ["bass", "session", "indie", "recording"],
    createdAt: "2024-01-18",
    updatedAt: "2024-01-18",
  },
]

export default function PostedOpportunitiesPage() {
  const [statusFilter, setStatusFilter] = useState<string>("")

  const filteredOpportunities = mockPostedOpportunities.filter((opportunity) => {
    return !statusFilter || opportunity.status === statusFilter
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "open":
        return <Badge className="bg-green-100 text-green-800 hover:bg-green-100">Open</Badge>
      case "closed":
        return <Badge variant="secondary">Closed</Badge>
      case "filled":
        return <Badge className="bg-blue-100 text-blue-800 hover:bg-blue-100">Filled</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="px-4 md:px-8 lg:px-16 py-8">
        {/* Header */}
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold">My Posted Opportunities</h1>
            <p className="text-muted-foreground">Manage your posted gigs and applications</p>
          </div>
          <Link href="/opportunities/new">
            <Button className="gap-2">
              <Plus className="w-4 h-4" />
              Post New Opportunity
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card className="mb-6">
          <CardContent className="pt-6">
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium">Filter by status:</span>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="All statuses" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All statuses</SelectItem>
                  <SelectItem value="open">Open</SelectItem>
                  <SelectItem value="closed">Closed</SelectItem>
                  <SelectItem value="filled">Filled</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Posted Opportunities List */}
        <div className="space-y-4">
          {filteredOpportunities.map((opportunity) => (
            <Card key={opportunity.id} className="hover:shadow-md transition-shadow">
              <CardContent className="pt-6">
                <div className="flex flex-col lg:flex-row gap-4">
                  <div className="flex-1">
                    <div className="flex items-start justify-between mb-4">
                      <div>
                        <h3 className="text-xl font-semibold mb-1">{opportunity.title}</h3>
                        <p className="text-muted-foreground text-sm mb-2">Role: {opportunity.role}</p>
                        <div className="flex flex-wrap gap-2 mb-3">
                          {opportunity.genre.map((genre: string) => (
                            <Badge key={genre} variant="secondary">
                              {genre}
                            </Badge>
                          ))}
                          {getStatusBadge(opportunity.status)}
                        </div>
                      </div>
                    </div>

                    <p className="text-muted-foreground mb-4 line-clamp-2">{opportunity.description}</p>

                    <div className="flex flex-wrap items-center gap-4 text-sm text-muted-foreground mb-4">
                      <div className="flex items-center gap-1">
                        <Calendar className="w-4 h-4" />
                        <span>Posted {opportunity.createdAt ? new Date(opportunity.createdAt).toLocaleDateString() : 'Unknown'}</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Users className="w-4 h-4" />
                        <span>{opportunity.applicationsCount} applicants</span>
                      </div>
                      {typeof opportunity.payment === 'object' && opportunity.payment.type === "paid" && (
                        <div className="flex items-center gap-1">
                          <DollarSign className="w-4 h-4" />
                          <span>
                            {opportunity.payment.currency}
                            {opportunity.payment.amount?.toLocaleString()}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2">
                      <span className="text-sm font-medium">Timeline:</span>
                      <span className="text-sm text-muted-foreground">
                        {new Date(opportunity.timeline.startDate).toLocaleDateString()} -
                        {new Date(opportunity.timeline.endDate).toLocaleDateString()}
                      </span>
                    </div>
                  </div>

                  <div className="flex flex-col gap-2 lg:w-auto w-full">
                    <Link href={`/opportunities/${opportunity.id}/applications`}>
                      <Button variant="outline" className="w-full gap-2">
                        <Users className="w-4 h-4" />
                        View Applications ({opportunity.applicationsCount})
                      </Button>
                    </Link>
                    <Link href={`/opportunities/${opportunity.id}`}>
                      <Button variant="outline" className="w-full gap-2">
                        <Eye className="w-4 h-4" />
                        View Details
                      </Button>
                    </Link>
                    <Button variant="outline" className="w-full gap-2">
                      <Edit className="w-4 h-4" />
                      Edit
                    </Button>
                    <Button variant="outline" className="w-full gap-2 text-destructive hover:text-destructive">
                      <Trash2 className="w-4 h-4" />
                      Delete
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {filteredOpportunities.length === 0 && (
          <Card className="text-center py-12">
            <CardContent>
              <Users className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No opportunities found</h3>
              <p className="text-muted-foreground mb-4">
                {statusFilter
                  ? `No opportunities with status "${statusFilter}"`
                  : "You haven't posted any opportunities yet."}
              </p>
              <Link href="/opportunities/new">
                <Button>Post Your First Opportunity</Button>
              </Link>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  )
}
