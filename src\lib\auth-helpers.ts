import type { AuthErrorCode, AuthError } from '@/types/auth'

// Password validation utilities
export const passwordRequirements = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: false, // AWS Cognito default doesn't require special chars
}

export function validatePassword(password: string): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  if (password.length < passwordRequirements.minLength) {
    errors.push(`Password must be at least ${passwordRequirements.minLength} characters long`)
  }

  if (passwordRequirements.requireUppercase && !/[A-Z]/.test(password)) {
    errors.push('Password must contain at least one uppercase letter')
  }

  if (passwordRequirements.requireLowercase && !/[a-z]/.test(password)) {
    errors.push('Password must contain at least one lowercase letter')
  }

  if (passwordRequirements.requireNumbers && !/[0-9]/.test(password)) {
    errors.push('Password must contain at least one number')
  }

  if (passwordRequirements.requireSpecialChars && !/[!@#$%^&*(),.?":{}|<>]/.test(password)) {
    errors.push('Password must contain at least one special character')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

// Email validation
export function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// Auth error handling utilities
export function mapAuthError(error: Error): AuthError {
  const errorName = error.name as AuthErrorCode

  const errorMessages: Record<AuthErrorCode, string> = {
    UserNotConfirmedException: 'Please verify your email address. Check your email for a confirmation link.',
    NotAuthorizedException: 'Incorrect email or password. Please check your credentials and try again.',
    UserNotFoundException: 'No account found with this email address. Please check your email or sign up.',
    InvalidParameterException: 'Invalid parameters provided. Please check your input.',
    TooManyRequestsException: 'Too many requests. Please wait a moment and try again.',
    UsernameExistsException: 'An account with this email already exists. Please try signing in instead.',
    InvalidPasswordException: 'Password does not meet requirements. Please ensure it meets all criteria.',
    CodeMismatchException: 'Invalid verification code. Please check the code and try again.',
    ExpiredCodeException: 'Verification code has expired. Please request a new code.',
    UnknownError: 'An unexpected error occurred. Please try again.',
  }

  const code = Object.keys(errorMessages).includes(errorName) 
    ? errorName 
    : 'UnknownError' as AuthErrorCode

  return {
    code,
    message: errorMessages[code],
    originalError: error
  }
}

// Route protection utilities
export function getAuthRedirectPath(isAuthenticated: boolean, currentPath: string): string | null {
  // Public routes that don't require authentication
  const publicPaths = ['/login', '/signup', '/forgot-password']

  // If user is authenticated and on a public auth page, redirect to dashboard
  if (isAuthenticated && publicPaths.includes(currentPath)) {
    return '/'
  }

  // If user is not authenticated and NOT on a public page, redirect to login
  // This approach is more secure - assume all routes require auth unless explicitly public
  if (!isAuthenticated && !publicPaths.includes(currentPath)) {
    return '/login'
  }

  return null
}

// Storage utilities for auth state persistence
export const authStorage = {
  setRememberMe: (remember: boolean) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_remember_me', remember.toString())
    }
  },

  getRememberMe: (): boolean => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_remember_me') === 'true'
    }
    return false
  },

  clearRememberMe: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_remember_me')
    }
  },

  setLastEmail: (email: string) => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_last_email', email)
    }
  },

  getLastEmail: (): string | null => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('auth_last_email')
    }
    return null
  },

  clearLastEmail: () => {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_last_email')
    }
  }
}

// Form validation utilities
export function getPasswordStrength(password: string): {
  score: number // 0-4
  label: string
  color: string
} {
  let score = 0
  
  if (password.length >= 8) score++
  if (/[a-z]/.test(password)) score++
  if (/[A-Z]/.test(password)) score++
  if (/[0-9]/.test(password)) score++
  if (/[!@#$%^&*(),.?":{}|<>]/.test(password)) score++

  const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong']
  const colors = ['red', 'orange', 'yellow', 'blue', 'green']

  return {
    score: Math.min(score, 4),
    label: labels[Math.min(score, 4)],
    color: colors[Math.min(score, 4)]
  }
}

// Utility to format user display name
export function formatUserDisplayName(user: { username: string; email?: string }): string {
  if (user.email && user.email !== user.username) {
    return user.email
  }
  return user.username
}

// Utility to mask email for display
export function maskEmail(email: string): string {
  const [localPart, domain] = email.split('@')
  if (localPart.length <= 2) {
    return `${localPart[0]}***@${domain}`
  }
  return `${localPart.slice(0, 2)}***@${domain}`
}
