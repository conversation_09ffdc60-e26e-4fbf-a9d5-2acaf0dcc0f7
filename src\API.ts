/* tslint:disable */
/* eslint-disable */
//  This file was automatically generated and should not be edited.

export type Artist = {
  __typename: "Artist",
  id: string,
  name: string,
  bio?: string | null,
  formedDate?: string | null,
  disbandedDate?: string | null,
  // Nullable
  location?: string | null,
};

export type ArtistProfile = {
  __typename: "ArtistProfile",
  artist?: Artist | null,
  songs?:  Array<Song | null > | null,
  recordings?:  Array<Recording | null > | null,
  albums?:  Array<Album | null > | null,
};

export type Song = {
  __typename: "Song",
  id: string,
  title?: string | null,
  duration?: number | null,
  recordID?: string | null,
  releaseDate?: string | null,
  coverPhoto?: string | null,
  role?: string | null,
  credits?:  Array<Credit | null > | null,
  recordings?:  Array<Recording | null > | null,
};

export type Credit = {
  __typename: "Credit",
  artistId?: string | null,
  role?: string | null,
  name?: string | null,
};

export type Recording = {
  __typename: "Recording",
  id: string,
  title?: string | null,
  duration?: number | null,
  recordID?: string | null,
  releaseDate?: string | null,
  coverPhoto?: string | null,
  credits?:  Array<Credit | null > | null,
};

export type Album = {
  __typename: "Album",
  id: string,
  title?: string | null,
  releaseDate?: string | null,
  genre?: Array< string | null > | null,
  description?: string | null,
  coverArtData?: string | null,
  tracks?:  Array<Track | null > | null,
};

export type Track = {
  __typename: "Track",
  trackPosition?: number | null,
  recording?: Recording | null,
};

export type RecordingPage = {
  __typename: "RecordingPage",
  recording?: Recording | null,
  performedBy?:  Array<Artist | null > | null,
  song?: Song | null,
  similarRecordings?:  Array<Recording | null > | null,
};

export type GetAllArtistQueryVariables = {
};

export type GetAllArtistQuery = {
  // Gets a list of all artists.
  getAllArtist:  Array< {
    __typename: "Artist",
    id: string,
    name: string,
    bio?: string | null,
    formedDate?: string | null,
    disbandedDate?: string | null,
    // Nullable
    location?: string | null,
  } >,
};

export type SearchArtistByNameQueryVariables = {
  name: string,
};

export type SearchArtistByNameQuery = {
  // Searches for artists by name (partial, case-insensitive match).
  searchArtistByName:  Array< {
    __typename: "Artist",
    id: string,
    name: string,
    bio?: string | null,
    formedDate?: string | null,
    disbandedDate?: string | null,
    // Nullable
    location?: string | null,
  } >,
};

export type GetArtistProfileQueryVariables = {
  artistId: string,
};

export type GetArtistProfileQuery = {
  getArtistProfile?:  {
    __typename: "ArtistProfile",
    artist?:  {
      __typename: "Artist",
      id: string,
      name: string,
      bio?: string | null,
      formedDate?: string | null,
      disbandedDate?: string | null,
      // Nullable
      location?: string | null,
      dp?: string | null,

    } | null,
    songs?:  Array< {
      __typename: "Song",
      id: string,
      title?: string | null,
      duration?: number | null,
      recordID?: string | null,
      releaseDate?: string | null,
      coverPhoto?: string | null,
      role?: string | null,
    } | null > | null,
    recordings?:  Array< {
      __typename: "Recording",
      id: string,
      title?: string | null,
      duration?: number | null,
      recordID?: string | null,
      releaseDate?: string | null,
      coverPhoto?: string | null,
      role?: string | null,
    } | null > | null,
    albums?:  Array< {
      __typename: "Album",
      id: string,
      title?: string | null,
      releaseDate?: string | null,
      genre?: Array< string | null > | null,
      description?: string | null,
      coverArtData?: string | null,
    } | null > | null,
  } | null,
};

export type GetAlbumQueryVariables = {
  albumId: string,
};

export type GetAlbumQuery = {
  // Get Full Album Details
  getAlbum?:  {
    __typename: "Album",
    id: string,
    title?: string | null,
    releaseDate?: string | null,
    genre?: Array< string | null > | null,
    description?: string | null,
    coverArtData?: string | null,
    tracks?:  Array< {
      __typename: "Track",
      trackPosition?: number | null,
    } | null > | null,
  } | null,
};

export type GetSongQueryVariables = {
  songId: string,
};

export type GetSongQuery = {
  getSong?:  {
    __typename: "Song",
    id: string,
    title?: string | null,
    duration?: number | null,
    recordID?: string | null,
    releaseDate?: string | null,
    coverPhoto?: string | null,
    role?: string | null,
    credits?:  Array< {
      __typename: "Credit",
      artistId?: string | null,
      role?: string | null,
      name?: string | null,
    } | null > | null,
    recordings?:  Array< {
      __typename: "Recording",
      id: string,
      title?: string | null,
      duration?: number | null,
      recordID?: string | null,
      releaseDate?: string | null,
      coverPhoto?: string | null,
    } | null > | null,
  } | null,
};

export type GetRecordingQueryVariables = {
  recordingId: string,
};

export type GetRecordingQuery = {
  getRecording?:  {
    __typename: "RecordingPage",
    recording?:  {
      __typename: "Recording",
      id: string,
      title?: string | null,
      duration?: number | null,
      recordID?: string | null,
      releaseDate?: string | null,
      coverPhoto?: string | null,
      credits?: Credit
    } | null,
    performedBy?:  Array< {
      __typename: "Artist",
      id: string,
      name: string,
      bio?: string | null,
      formedDate?: string | null,
      disbandedDate?: string | null,
      // Nullable
      location?: string | null,
    } | null > | null,
    song?:  {
      __typename: "Song",
      id: string,
      title?: string | null,
      duration?: number | null,
      recordID?: string | null,
      releaseDate?: string | null,
      coverPhoto?: string | null,
      role?: string | null,
      credits?: Credit
    } | null,
    similarRecordings?:  Array< {
      __typename: "Recording",
      id: string,
      title?: string | null,
      duration?: number | null,
      recordID?: string | null,
      releaseDate?: string | null,
      coverPhoto?: string | null,
    } | null > | null,
  } | null,
};
