'use client';
import DashboardContextPanel from "./dashboard-context-panel";
import DashboardMainContent from "./dashboard-main-content";
// import { useRouter } from 'next/router';

export default function HomePage() {
  // const router = useRouter();

  // if(localStorage.getItem("onboarding")!=="done"){
  //   router.push('/onboarding');
  //   return null;
  //   }

  return (
    <div className="min-h-screen bg-background text-foreground flex">
      {/* Left contextual panel */}
      <DashboardContextPanel />
      {/* Main dashboard content */}
      <DashboardMainContent />
    </div>
  );
}
