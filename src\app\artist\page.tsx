
'use client'

import { useSearchParams } from 'next/navigation'
import { Suspense } from 'react'
import ArtistListPage from "@/features/artist/components/artist-list-page"
import ArtistDetailsPage from "@/features/artist/components/artist-details-page"

function ArtistContent() {
  const searchParams = useSearchParams()
  const artistId = searchParams.get('id')

  if (!artistId) {
    return <ArtistListPage />
  }

  return <ArtistDetailsPage artistId={artistId} />
}

export default function Artist() {
  return (
    <Suspense fallback={
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Loading artist...</p>
        </div>
      </div>
    }>
      <ArtistContent />
    </Suspense>
  )
}
