'use client';

import { Users } from "lucide-react";
import React from "react";
import { useRouter } from "next/navigation";

const navItems = [
  // { label: "Feed", icon: UserCircle },

  { label: "Artists", icon: Users, path: "/discover/artist" },
  // { label: "Tracks", icon: Music, path: "/discover/tracks" },
  // { label: "Gigs", icon: Calendar, path: "/discover/gigs" },
];

export default function DiscoverContextPanel({ active = "Artists" }: { active?: string }) {
  const router = useRouter();
  return (
<aside className="hidden md:flex w-72 min-w-[16rem] max-w-xs border-r bg-background/50 p-6 flex-col gap-6">
      <h2 className="text-xl font-bold mb-2">Discover</h2>
      <nav className="flex flex-col gap-1">
        {navItems.map((item) => {
          const isActive = active === item.label;
          return (
            <button
              key={item.label}
              className={`flex items-center gap-3 px-4 py-2 rounded-lg transition text-left font-medium text-base hover:bg-muted ${isActive ? "bg-muted text-primary" : "text-muted-foreground"}`}
              onClick={() => router.push(item.path)}
              type="button"
            >
              <item.icon className="w-5 h-5" />
              {item.label}
            </button>
          );
        })}
      </nav>
    </aside>
  );
} 