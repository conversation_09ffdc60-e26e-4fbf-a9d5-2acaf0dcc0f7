"use client";
import {
  Search,
  HelpCircle,
  Menu,
} from "lucide-react"
import React from "react";

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/components/ui/sidebar"
import { isAuthOnlyRoute, isOnboarding } from "@/lib/auth-routes";
import { usePathname } from "next/navigation";

// Main navigation items
const mainMenuItems = [
  // {
  //   title: "Dashboard",
  //   url: "/",
  //   icon: Home,
  // },
  {
    title: "Discover",
    url: "/discover",
    icon: Search,
  },
  // {
  //   title: "Gigs",
  //   url: "#",
  //   icon: Calendar,
  // },
  // {
  //   title: "Studio",
  //   url: "#",
  //   icon: Guitar,
  // },
  // {
  //   title: "Chat",
  //   url: "#",
  //   icon: MessageCircle,
  // },
  // {
  //   title: "Collaborator",
  //   url: "#",
  //   icon: User,
  // },
  // {
  //   title: "Community",
  //   url: "#",
  //   icon: Users,
  // },
  // {
  //   title: "Pricing",
  //   url: "#",
  //   icon: DollarSign,
  // },
]

// Footer items
const footerMenuItems = [
  {
    title: "Support",
    url: "#",
    icon: HelpCircle,
  }
]

export function AppSidebar() {
  const pathname = usePathname();

  if(isAuthOnlyRoute(pathname) || isOnboarding(pathname)){
    return null;
  }

  return (
    <Sidebar collapsible="none" className="w-22 h-screen flex flex-col">
      <SidebarHeader>
        <div className="flex items-center justify-center py-6">
          <Menu className="h-5 w-5" />
        </div>
      </SidebarHeader>
      <SidebarContent className="flex-1 px-1 py-4">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {mainMenuItems.map((item) => (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton
                    asChild
                    isActive={pathname === item.url}
                    className="h-16 flex-col gap-1 text-xs"
                  >
                    <a href={item.url} className="flex flex-col items-center gap-1">
                      <item.icon className="h-5 w-5" />
                      <span className="text-xs text-center leading-tight">{item.title}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ))}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarContent>
      <SidebarFooter className="px-1 py-4 mt-auto">
        <SidebarGroup>
          <SidebarGroupContent>
            <SidebarMenu className="space-y-1">
              {footerMenuItems.map((item) => (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      isActive={pathname === item.url}
                      className="h-16 flex-col gap-1 text-xs"
                    >
                      <a href={item.url} className="flex flex-col items-center gap-1">
                        <item.icon className="h-5 w-5" />
                        <span className="text-xs text-center leading-tight">{item.title}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                )
              )}
            </SidebarMenu>
          </SidebarGroupContent>
        </SidebarGroup>
      </SidebarFooter>
    </Sidebar>
  )
}