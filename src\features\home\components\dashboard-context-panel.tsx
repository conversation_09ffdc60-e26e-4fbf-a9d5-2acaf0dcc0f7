import { Guitar, MessageCircle, PlusCircle } from "lucide-react";

export default function DashboardContextPanel() {
  return (
    <aside className="w-72 min-w-[16rem] max-w-xs border-r bg-background/50 p-6 flex flex-col gap-6">
      <h2 className="text-xl font-bold mb-2">Dashboard</h2>
      <div className="flex flex-col gap-4">
        <div className="flex items-start gap-3 cursor-pointer hover:bg-muted rounded-lg p-3 transition">
          <span className="bg-primary/10 rounded-lg p-2 flex items-center justify-center">
            <PlusCircle className="w-6 h-6 text-primary" />
          </span>
          <div>
            <div className="font-semibold">Create New Project</div>
            <div className="text-xs text-muted-foreground">Start a music project with talented artists!</div>
          </div>
        </div>
        <div className="flex items-start gap-3 cursor-pointer hover:bg-muted rounded-lg p-3 transition">
          <span className="bg-primary/10 rounded-lg p-2 flex items-center justify-center">
            <Guitar className="w-6 h-6 text-primary" />
          </span>
          <div>
            <div className="font-semibold">Post New Gig</div>
            <div className="text-xs text-muted-foreground">Post a new gig featuring amazing artists!</div>
          </div>
        </div>
        <div className="flex items-start gap-3 cursor-pointer hover:bg-muted rounded-lg p-3 transition">
          <span className="bg-primary/10 rounded-lg p-2 flex items-center justify-center">
            <MessageCircle className="w-6 h-6 text-primary" />
          </span>
          <div>
            <div className="font-semibold">Share New Track</div>
            <div className="text-xs text-muted-foreground">Share your latest track and collaborate!</div>
          </div>
        </div>
      </div>
    </aside>
  );
}
