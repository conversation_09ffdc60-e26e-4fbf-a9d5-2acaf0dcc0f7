import React from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useOnboarding } from '@/features/onboarding/onboarding-context';

export default function Step3() {
  const { step, setStep, data, setData } = useOnboarding();
  const bio = data.bio || '';

  return (
    <div className="flex flex-col w-full max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4 mt-2">Describe yourself</h1>
      <div className="mb-8">
        <Label className="mb-1 text-foreground">Write a short bio best describing you</Label>
        <Textarea
          placeholder="e.g. I am an enthusiastic song writer"
          value={bio}
          onChange={e => setData({ bio: e.target.value })}
          className="min-h-[100px] resize-none placeholder-muted-foreground bg-card text-foreground border border-input"
        />
      </div>
      <div className="flex w-full justify-between gap-2">
        <Button variant="outline" className="w-32" onClick={() => setStep(step - 1)}>Back</Button>
        <Button className="w-32" onClick={() => setStep(step + 1)} disabled={!bio.trim()}>Continue</Button>
      </div>
    </div>
  );
}
