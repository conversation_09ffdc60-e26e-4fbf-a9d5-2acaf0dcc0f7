import { generateClient } from 'aws-amplify/api';
import { Amplify } from 'aws-amplify';

// Configure Amplify for build-time usage (server-side)
if (typeof window === 'undefined') {
  // Server-side configuration for build time
  Amplify.configure({
    API: {
      GraphQL: {
        endpoint: 'https://2xymbka4zzam3b347bgugpfp6a.appsync-api.us-east-2.amazonaws.com/graphql',
        region: 'us-east-2',
        defaultAuthMode: 'apiKey' as const,
        apiKey: 'da2-hu56cxlaxreutk6qxxkxhab7ly'
      }
    }
  });
}

export const client = generateClient();