"use client";
import React, { useEffect, useState } from 'react';
import { OnboardingProvider } from '@/features/onboarding/onboarding-context';
import { OnboardingStepper } from '@/features/onboarding/onboarding-stepper';
import dynamic from 'next/dynamic';
import { useRouter } from 'next/navigation';
import { Loader2 } from 'lucide-react';

const steps = [
  dynamic(() => import('@/features/onboarding/steps/step-1')),
  dynamic(() => import('@/features/onboarding/steps/step-2')),
  dynamic(() => import('@/features/onboarding/steps/step-3')),
  dynamic(() => import('@/features/onboarding/steps/step-4')),
  dynamic(() => import('@/features/onboarding/steps/step-5')),
  dynamic(() => import('@/features/onboarding/steps/step-6')),
  dynamic(() => import('@/features/onboarding/steps/step-7')),
  dynamic(() => import('@/features/onboarding/steps/step-8')),
];

import { useOnboarding } from '@/features/onboarding/onboarding-context';

function OnboardingSteps() {
  const { step } = useOnboarding();
  const StepComponent = steps[step - 1];

  return (
    <div className="fixed inset-0 w-full h-full flex flex-col items-center justify-center bg-background text-foreground z-50">
      <div className="w-full max-w-md px-4 py-10">
        <div className="mb-6">
          <div className="text-sm text-muted-foreground mb-2">Step {step}/8</div>
          <OnboardingStepper />
        </div>
        <StepComponent />
      </div>
    </div>
  );
}

export default function OnboardingPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check onboarding status on client side only
    const checkOnboardingStatus = () => {
      try {
        const onboardingStatus = localStorage.getItem("onboarding");

        if (onboardingStatus === "done") {
          router.push('/');
          return;
        }

        setIsLoading(false);
      } catch (error) {
        console.error("Error checking onboarding status:", error);
        setIsLoading(false);
      }
    };

    // Add a small delay to ensure the page is fully mounted
    const timer = setTimeout(checkOnboardingStatus, 100);
    return () => clearTimeout(timer);
  }, [router]);

  // Show loading while checking onboarding status
  if (isLoading) {
    return (
      <div className="fixed inset-0 w-full h-full flex items-center justify-center bg-background text-foreground z-50">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4 text-primary" />
          <h2 className="text-xl font-semibold">Loading...</h2>
          <p className="text-muted-foreground">Checking your onboarding status</p>
        </div>
      </div>
    );
  }

  return (
    <OnboardingProvider>
      <OnboardingSteps />
    </OnboardingProvider>
  );
}
