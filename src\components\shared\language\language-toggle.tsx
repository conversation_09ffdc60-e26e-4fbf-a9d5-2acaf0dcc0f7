'use client';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Locale } from '@/i18n/config';
import { useI18n } from '@/contexts/i18n/i18n-context';
import clsx from 'clsx';
import { useTranslations } from 'next-intl';

function LanguageToggle() {
  const t = useTranslations('LanguageToggle');
  const { locale, setLocale, isLoading } = useI18n();

  function onChange(value: string) {
    const newLocale = value as Locale;
    setLocale(newLocale);
  }

  const items:  Array<{value: string; label: string}> = [
        {
          value: 'en',
          label: t('english')
        },
        {
          value: 'es',
          label: t('spanish')
        },
        {
          value: 'fr',
          label: t('french')
        }
      ]


  return (
    <Select value={locale} onValueChange={onChange}>
      <SelectTrigger
       className={clsx(
  'w-full justify-start rounded-sm px-3 py-2 text-sm transition-colors',
  'bg-background text-foreground hover:bg-muted',
  isLoading && 'pointer-events-none opacity-60' // only for language
)}

      >
        <SelectValue  placeholder={t("selectLanguage")} />
      </SelectTrigger>
      <SelectContent className="bg-background text-foreground">
        <SelectGroup>
          {items.map((item) => (
            <SelectItem
              key={item.value}
             className={clsx(
  'flex cursor-default items-center px-3 py-2 text-sm',
  'data-[highlighted]:bg-muted'
)}

              value={item.value}
            >
              <span>{item.label}</span>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}

export default LanguageToggle;
