'use client';

import { useQuery, gql } from '@apollo/client';

const USERS_QUERY = gql`
  query Users {
    users {
      id
      name
      email
    }
  }
`;

export default function UsersList() {
  const { data, loading, error } = useQuery(USERS_QUERY);

  if (loading) return <div>Loading users...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!data?.users || data.users.length === 0) return <div>No users found.</div>;

  return (
    <ul>
      {data.users.map((user: { id: string; name: string; email: string }) => (
        <li key={user.id}>
          <strong>{user.name}</strong> <span>({user.email})</span>
        </li>
      ))}
    </ul>
  );
}
