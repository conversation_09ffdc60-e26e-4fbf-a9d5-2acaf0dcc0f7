import React from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { ChevronLeft, ChevronRight } from "lucide-react";

export interface Track {
  title: string;
  // Add more fields as needed
}

interface TrendingTracksCardProps {
  tracks: Track[];
  onPrev?: () => void;
  onNext?: () => void;
}

const TrendingTracksCard: React.FC<TrendingTracksCardProps> = ({ tracks, onPrev, onNext }) => (
  <Card className="p-6 flex flex-col gap-4 rounded-l shadow-sm">
    <div className="flex items-center justify-between mb-2">
      <h2 className="text-2xl font-bold">Trending Tracks</h2>
      <div className="flex gap-2">
        <Button variant="ghost" size="icon" onClick={onPrev}><ChevronLeft className="w-5 h-5" /></Button>
        <Button variant="ghost" size="icon" onClick={onNext}><ChevronRight className="w-5 h-5" /></Button>
      </div>
    </div>
    <div className="flex gap-4 overflow-x-auto pb-2">
      {tracks.length === 0
        ? [...Array(5)].map((_, i) => <Skeleton key={i} className="w-48 h-32 rounded-lg" />)
        : tracks.map((track, i) => (
            <div key={i} className="w-48 h-32 rounded-lg bg-muted flex items-center justify-center">{track.title}</div>
          ))}
    </div>
  </Card>
);

export default TrendingTracksCard; 