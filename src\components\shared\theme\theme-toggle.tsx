'use client';

import {
  Select,
  SelectTrigger,
  SelectContent,
  SelectGroup,
  SelectItem,
} from '@/components/ui/select';
import { useTheme } from '@/contexts/theme/theme-context';
import { useEffect, useState } from 'react';
import { Sun, Moon, Laptop2 } from 'lucide-react';
import clsx from 'clsx';

const themeOptions = [
  {
    label: 'Light',
    value: 'light',
    icon: Sun,
  },
  {
    label: 'Dark',
    value: 'dark',
    icon: Moon,
  },
  {
    label: 'System',
    value: 'system',
    icon: Laptop2,
  },
] as const;

export default function ThemeToggle() {
  const { theme, setTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="w-[140px] h-9 rounded-lg bg-gray-100 dark:bg-gray-800 animate-pulse" />
    );
  }

  const selectedOption = themeOptions.find((item) => item.value === theme);

  return (
    <Select value={theme} onValueChange={setTheme}>
      <SelectTrigger
        className={clsx(
  'w-full justify-start rounded-sm px-3 py-2 text-sm transition-colors',
  'bg-background text-foreground hover:bg-muted'
)}

      >
        <div className="flex items-center space-x-2">
  {selectedOption?.icon && (
    <selectedOption.icon className="w-4 h-4 text-muted-foreground" />
  )}
  <span>{selectedOption?.label}</span>
</div>

      </SelectTrigger>

      <SelectContent className="bg-background text-foreground">
        <SelectGroup>
          {themeOptions.map(({ value, label, icon: Icon }) => (
            <SelectItem
              key={value}
              value={value}
              className={clsx(
  'flex cursor-default items-center px-3 py-2 text-sm',
  'data-[highlighted]:bg-muted'
)}

            >
              <div className="flex items-center space-x-2">
                <Icon className="w-4 h-4" />
                <span>{label}</span>
              </div>
            </SelectItem>
          ))}
        </SelectGroup>
      </SelectContent>
    </Select>
  );
}
