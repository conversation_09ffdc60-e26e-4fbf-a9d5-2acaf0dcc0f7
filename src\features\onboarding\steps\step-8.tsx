import React from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useOnboarding } from '@/features/onboarding/onboarding-context';
import { useRouter } from 'next/navigation';

const ARTISTS = [
  '<PERSON><PERSON>', '<PERSON>', 'The Weeknd',
  '<PERSON><PERSON> Chour', '<PERSON><PERSON>', '<PERSON>',
  '<PERSON> Swift', '<PERSON>', '<PERSON><PERSON>R Rahman',
];

export default function Step8() {
  const { step, setStep, data, setData } = useOnboarding();
  const [search, setSearch] = React.useState('');
  const selected = data.followArtists || [];
  const router = useRouter();

  const filtered = ARTISTS.filter(a => a.toLowerCase().includes(search.toLowerCase()));

  const toggle = (artist: string) => {
    setData({ followArtists: selected.includes(artist) ? selected.filter(a => a !== artist) : [...selected, artist] });
  };

  const handleFinish = () => {
    console.log("data all===>>",data);
    
    // Here you have all onboarding data in 'data'
    // You can send it to your API or handle as needed
    // Example: await fetch('/api/onboarding', { method: 'POST', body: JSON.stringify(data) })
    
    localStorage.setItem("onboarding", "done");
    router.push('/');
  };

  return (
    <div className="flex flex-col w-full max-w-md mx-auto">
      <h1 className="text-2xl font-bold mb-4 mt-2">Follow artists you like</h1>
      <div className="text-sm text-muted-foreground mb-2">Follow 5 or more artists you like</div>
      <Input
        placeholder="Search artists"
        value={search}
        onChange={e => setSearch(e.target.value)}
        className="mb-6 bg-card text-foreground border border-input placeholder-muted-foreground"
      />
      <div className="grid grid-cols-3 gap-6 mb-8">
        {filtered.map(a => (
          <div
            key={a}
            className={`flex flex-col items-center cursor-pointer p-2`}
            onClick={() => toggle(a)}
          >
            <div className={`w-20 h-20 rounded-full flex items-center justify-center mb-2 border-2 transition-colors
              ${selected.includes(a)
                ? 'border-primary ring-2 ring-primary bg-primary/20'
                : 'border-border bg-muted'}`}></div>
            <span className="text-xs text-center mt-1 text-foreground">{a}</span>
          </div>
        ))}
      </div>
      <div className="flex w-full justify-between gap-2">
        <Button variant="outline" className="w-32" onClick={() => setStep(step - 1)}>Back</Button>
        <Button className="w-32" disabled={selected.length < 5} onClick={handleFinish}>Continue</Button>
      </div>
    </div>
  );
}
